prefix=/Users/<USER>/workspaces/openssl_for_ios_and_android/tools/../output/android/openssl-android-arm64-v8a
exec_prefix=${prefix}
libdir=${exec_prefix}/lib
includedir=${prefix}/include

Name: OpenSSL-libssl
Description: Secure Sockets Layer and cryptography libraries
Version: 1.1.0c
Requires.private: libcrypto
Libs: -L${libdir} -lssl
Libs.private: -L/Users/<USER>/workspaces/openssl_for_ios_and_android/tools/arm64-v8a-android-toolchain/sysroot/usr/lib -lz -ldl 
Cflags: -I${includedir}
