prefix=/Users/<USER>/workspaces/openssl_for_ios_and_android/tools/../output/android/openssl-android-armeabi
exec_prefix=${prefix}
libdir=${exec_prefix}/lib
includedir=${prefix}/include
enginesdir=${libdir}/engines-1.1

Name: OpenSSL-libcrypto
Description: OpenSSL cryptography library
Version: 1.1.0c
Libs: -L${libdir} -lcrypto
Libs.private: -L/Users/<USER>/workspaces/openssl_for_ios_and_android/tools/armeabi-android-toolchain/sysroot/usr/lib -lz -ldl 
Cflags: -I${includedir}
