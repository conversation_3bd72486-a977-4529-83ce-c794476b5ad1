prefix=/Users/<USER>/workspaces/openssl_for_ios_and_android/tools/../output/android/openssl-android-x86_64
exec_prefix=${prefix}
libdir=${exec_prefix}/lib
includedir=${prefix}/include

Name: OpenSSL-libssl
Description: Secure Sockets Layer and cryptography libraries
Version: 1.1.0c
Requires.private: libcrypto
Libs: -L${libdir} -lssl
Libs.private: -L/Users/<USER>/workspaces/openssl_for_ios_and_android/tools/x86_64-android-toolchain/sysroot/usr/lib -lz -ldl 
Cflags: -I${includedir}
