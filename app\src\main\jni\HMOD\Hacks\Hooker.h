#pragma once

static auto get_OnCameraHeightChanged(void *player) {
    auto (*_get_OnCameraHeightChanged)(void *player) = (void *(*)(void *))((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE(""), OBFUSCATE("CameraSystem"), OBFUSCATE("OnCameraHeightChanged"), 0));
    return _get_OnCameraHeightChanged(player);
}

void (*LateUpdate)(void *player);
void _LateUpdate(void *player) {
    if (player != nullptr) {
        get_OnCameraHeightChanged(player);
    }
    return LateUpdate(player);
}
float (*GetCameraHeightRateValue)(void *player, int type);
float _GetCameraHeightRateValue(void *player, int type) {
    if (player != nullptr) {
        if (CameraHeight == 0) {
            return 1.5f;
        } else if (CameraHeight == 1) {
            return 1.6f;
        } else if (CameraHeight == 2) {
            return 1.7f;
        } else if (CameraHeight == 3) {
            return 1.8f;
        } else if (CameraHeight == 4) {
            return 1.9f;
        } else if (CameraHeight == 5) {
            return 2.0f;
        } else if (CameraHeight == 6) {
            return 2.1f;
        } else if (CameraHeight == 7) {
            return 2.2f;
        } else if (CameraHeight == 8) {
            return 2.3f;
        } else if (CameraHeight == 9) {
            return 2.4f;
        } else if (CameraHeight == 10) {
            return 2.5f;
        } else if (CameraHeight == 11) {
            return 2.6f;
        } else if (CameraHeight == 12) {
            return 2.7f;
        } else if (CameraHeight == 13) {
            return 2.8f;
        } else if (CameraHeight == 14) {
            return 2.9f;
        } else if (CameraHeight == 15) {
            return 3.0f;
        }
    }
    return GetCameraHeightRateValue(player, type);
}


  /////FPS 60////

bool (*old_get_Supported60FPSMode)(void *instance);
bool get_Supported60FPSMode(void *instance) {
  //  if (instance != NULL && FPS60)
    {
        return true;
    }
    return old_get_Supported60FPSMode(instance);

}


#include <cmath>

#define OBFUSCATE_METHOD(image, namespaze, clazz, name, args) \
IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE(image), OBFUSCATE(namespaze), OBFUSCATE(clazz), OBFUSCATE(name), args)

#define OBFUSCATE_FIELD(image, namespaze, clazz, name) \
IL2Cpp::Il2CppGetFieldOffset(OBFUSCATE(image), OBFUSCATE(namespaze), OBFUSCATE(clazz), OBFUSCATE(name))




class ValueLinkerComponent {
    public:
        int get_actorHp() {
            int (*get_actorHp_)(ValueLinkerComponent * objLinkerWrapper) = (int (*)(ValueLinkerComponent *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ValueLinkerComponent", "get_actorHp", 0);
            return get_actorHp_(this);
        }

        int get_actorHpTotal() {
            int (*get_actorHpTotal_)(ValueLinkerComponent * objLinkerWrapper) =
                (int (*)(ValueLinkerComponent *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ValueLinkerComponent", "get_actorHpTotal", 0);
            return get_actorHpTotal_(this);
        }
};

class ActorConfig {
    public:
        int ConfigID() {
            return *(int *) ((uintptr_t) this + OBFUSCATE_FIELD("Project_d.dll", "Assets.Scripts.GameLogic", "ActorConfig", "ConfigID"));
        }
};

class ActorLinker {
    public:
        ValueLinkerComponent *ValueComponent() {
            return *(ValueLinkerComponent **)((uintptr_t)this + OBFUSCATE_FIELD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "ValueComponent"));
        }

        ActorConfig *ObjLinker() {
            return *(ActorConfig **) ((uintptr_t) this + OBFUSCATE_FIELD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "ObjLinker"));
        }

        Vector3 get_position() {
            Vector3 (*get_position_)(ActorLinker * linker) = (Vector3(*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_position", 0);
            return get_position_(this);
        }

        Quaternion get_rotation() {
            Quaternion (*get_rotation_)(ActorLinker *linker) = (Quaternion (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_rotation", 0);
            return get_rotation_(this);
        }

        bool IsHostCamp() {
            bool (*IsHostCamp_)(ActorLinker *linker) = (bool (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "IsHostCamp", 0);
            return IsHostCamp_(this);
        }

        bool IsHostPlayer() {
            bool (*IsHostPlayer_)(ActorLinker *linker) = (bool (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "IsHostPlayer", 0);
            return IsHostPlayer_(this);
        }

        bool isMoving() {
            return *(bool *) ((uintptr_t) this + OBFUSCATE_FIELD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "isMoving"));
        }

        Vector3 get_logicMoveForward() {
            Vector3 (*get_logicMoveForward_)(ActorLinker *linker) = (Vector3 (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_logicMoveForward", 0);
            return get_logicMoveForward_(this);
        }

        bool get_bVisible() {
            bool (*get_bVisible_)(ActorLinker *linker) = (bool (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_bVisible", 0);
            return get_bVisible_(this);
        }
};

class ActorManager {
    public:
        List<ActorLinker *> *GetAllHeros() {
            List<ActorLinker *> *(*_GetAllHeros)(ActorManager *actorManager) = (List<ActorLinker *> *(*)(ActorManager *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorManager", "GetAllHeros", 0);
            return _GetAllHeros(this);
        }
};

class KyriosFramework {
    public:
        static ActorManager *get_actorManager() {
            auto get_actorManager_ = (ActorManager *(*)())OBFUSCATE_METHOD("Project_d.dll", "Kyrios", "KyriosFramework", "get_actorManager", 0);
            return get_actorManager_();
        }
};

struct EntityInfo {
    Vector3 myPos;
    Vector3 enemyPos;
    Vector3 moveForward;
    int ConfigID;
    bool isMoving;
    int objId;  // Thêm objId để mapping với ESP cache
};

EntityInfo EnemyTarget;

// Hàm lấy vị trí thực từ ESP cache
Vector3 GetRealEnemyPosition(ActorLinker* actorLinker) {
    extern std::vector<EnemyData> enemyCache;
    extern std::mutex cacheMutex;
    extern int (*ActorLinker_get_ObjID)(void *instance);

    try {
        // Sử dụng ObjID thay vì ConfigID để mapping chính xác hơn
        int objId = ActorLinker_get_ObjID(actorLinker);

        // Tìm trong ESP cache
        std::lock_guard<std::mutex> lock(cacheMutex);
        for (const auto& enemy : enemyCache) {
            if (enemy.objId == objId && !enemy.isDead && enemy.hp > 0) {
                return enemy.position; // Vị trí thực từ ESP cache
            }
        }
    } catch(...) {
        // Fallback về get_position() nếu có lỗi
    }

    // Fallback về phương pháp cũ nếu không tìm thấy trong cache
    return actorLinker->get_position();
}

Vector3 RotateVectorByQuaternion(Quaternion q) {
    Vector3 v(0.0f, 0.0f, 1.0f);
    float w = q.w, x = q.x, y = q.y, z = q.z;

    Vector3 u(x, y, z);
    Vector3 cross1 = Vector3::Cross(u, v);
    Vector3 cross2 = Vector3::Cross(u, cross1);
    Vector3 result = v + 2.0f * cross1 * w + 2.0f * cross2;

    return result;
}

float SquaredDistance(Vector3 v, Vector3 o) {
    return (v.x - o.x) * (v.x - o.x) + (v.y - o.y) * (v.y - o.z) + (v.z - o.z) * (v.z - o.z);
}

Vector3 calculateSkillDirection(Vector3 myPosi, Vector3 enemyPosi, bool isMoving, Vector3 moveForward) {
    if (isMoving) {enemyPosi += moveForward;}
    Vector3 direction = enemyPosi - myPosi;
    direction.Normalize();
    return direction;
}

bool AimElsu = true;
bool isCharging;
int mode = 0, aimType = 1, drawType = 2, SkillSlott;



float getRange(int configID) {
    switch(configID) {
        case 196: return 25.f;
        case 108: return 13.f;
        case 157: return 13.f;
        case 175: return 13.f;
        case 545: return 13.f;
        default: return 25.f;
    }
}



Vector3 (*_GetUseSkillDirection)(void *instance, bool isTouchUse);
Vector3 GetUseSkillDirection(void *instance, bool isTouchUse) {
    if (instance != NULL && AimElsu) {
        if (EnemyTarget.ConfigID == 196 || EnemyTarget.ConfigID == 108 || EnemyTarget.ConfigID == 157 || EnemyTarget.ConfigID == 175 || EnemyTarget.ConfigID == 545) {
            if (EnemyTarget.myPos != Vector3::zero() && EnemyTarget.enemyPos != Vector3::zero() && SkillSlott == 2) {
                return calculateSkillDirection(EnemyTarget.myPos, EnemyTarget.enemyPos, EnemyTarget.isMoving, EnemyTarget.moveForward);
            }
        }
    }
    return _GetUseSkillDirection(instance, isTouchUse);
}

uintptr_t m_isCharging, m_currentSkillSlottType;
void (*_UpdateLogic)(void *instance, int delta);
void UpdateLogic(void *instance, int delta) {
     if (instance != NULL) {
        isCharging = *(bool *)((uintptr_t)instance + OBFUSCATE_FIELD("Project_d.dll", "Assets.Scripts.GameSystem", "CSkillButtonManager", "m_isCharging"));
            SkillSlott = *(int *)((uintptr_t)instance + OBFUSCATE_FIELD("Project_d.dll", "Assets.Scripts.GameSystem", "CSkillButtonManager", "m_currentSkillSlotType"));
    }
    if (AimElsu) {
        Quaternion rotation;
        float minDistance = std::numeric_limits<float>::infinity();
        float minDirection = std::numeric_limits<float>::infinity();
        float minHealth = std::numeric_limits<float>::infinity();
        float minHealth2 = std::numeric_limits<float>::infinity();
        float minHealthPercent = std::numeric_limits<float>::infinity();
        ActorLinker *Entity = nullptr;

        ActorManager *get_actorManager = KyriosFramework::get_actorManager();
        if (get_actorManager == nullptr) return;

        List<ActorLinker *> *GetAllHeros = get_actorManager->GetAllHeros();
        if (GetAllHeros == nullptr) return;

        ActorLinker **actorLinkers = (ActorLinker **) GetAllHeros->getItems();

        for (int i = 0; i < GetAllHeros->getSize(); i++) {
            ActorLinker *actorLinker = actorLinkers[(i *2) + 1];
            if (actorLinker == nullptr) continue;

            if (actorLinker->IsHostPlayer()) {
                rotation = actorLinker->get_rotation();
                EnemyTarget.myPos = actorLinker->get_position();
                EnemyTarget.ConfigID = actorLinker->ObjLinker()->ConfigID();
            }

            if (actorLinker->IsHostCamp() || actorLinker->ValueComponent()->get_actorHp() < 1) continue;

            // SỬ DỤNG VỊ TRÍ THỰC TỪ ESP CACHE
            Vector3 EnemyPos = GetRealEnemyPosition(actorLinker);
            float Health = actorLinker->ValueComponent()->get_actorHp();
            float MaxHealth = actorLinker->ValueComponent()->get_actorHpTotal();
            int HealthPercent = (int)std::round((float)Health / MaxHealth * 100);
            float Distance = Vector3::Distance(EnemyTarget.myPos, EnemyPos);
            float Direction = SquaredDistance(RotateVectorByQuaternion(rotation), calculateSkillDirection(EnemyTarget.myPos, EnemyPos, actorLinker->isMoving(), actorLinker->get_logicMoveForward()));

            float range = getRange(EnemyTarget.ConfigID);
            if (Distance < range) {
                if (aimType == 0) {
                    if (HealthPercent < minHealthPercent) {
                        Entity = actorLinker;
                        minHealthPercent = HealthPercent;
                    }

                    if (HealthPercent == minHealthPercent && Health < minHealth2) {
                        Entity = actorLinker;
                        minHealth2 = Health;
                        minHealthPercent = HealthPercent;
                    }
                }

                if (aimType == 1 && Health < minHealth) {
                    Entity = actorLinker;
                    minHealth = Health;
                }

                if (aimType == 2 && Distance < minDistance) {
                    Entity = actorLinker;
                    minDistance = Distance;
                }

                if (aimType == 3 && Direction < minDirection && isCharging) {
                    Entity = actorLinker;
                    minDirection = Direction;
                }
            }
        }

        if (Entity == nullptr) {
            EnemyTarget.enemyPos = Vector3::zero();
            EnemyTarget.moveForward = Vector3::zero();
            EnemyTarget.ConfigID = 0;
            EnemyTarget.isMoving = false;
        }

        if (Entity != NULL) {
            // SỬ DỤNG VỊ TRÍ THỰC TỪ ESP CACHE
            Vector3 realEnemyPos = GetRealEnemyPosition(Entity);
            float nDistance = Vector3::Distance(EnemyTarget.myPos, realEnemyPos);
            if (nDistance > getRange(EnemyTarget.ConfigID) || Entity->ValueComponent()->get_actorHp() < 1) {
                EnemyTarget.enemyPos = Vector3::zero();
                EnemyTarget.moveForward = Vector3::zero();
                EnemyTarget.objId = 0;
                minDistance = std::numeric_limits<float>::infinity();
                minDirection = std::numeric_limits<float>::infinity();
                minHealth = std::numeric_limits<float>::infinity();
                minHealth2 = std::numeric_limits<float>::infinity();
                minHealthPercent = std::numeric_limits<float>::infinity();
                Entity = nullptr;
            } else {
                // LƯU VỊ TRÍ THỰC TỪ ESP CACHE
                EnemyTarget.enemyPos = realEnemyPos;
                EnemyTarget.moveForward = Entity->get_logicMoveForward();
                EnemyTarget.isMoving = Entity->isMoving();
                EnemyTarget.objId = ActorLinker_get_ObjID(Entity);
            }
        }

        if (Entity != NULL && aimType == 3 && !isCharging) {
            EnemyTarget.enemyPos = Vector3::zero();
            EnemyTarget.moveForward = Vector3::zero();
            minDirection = std::numeric_limits<float>::infinity();
            Entity = nullptr;
        }

        if ((Entity != NULL || EnemyTarget.enemyPos != Vector3::zero()) && get_actorManager == nullptr) {
            EnemyTarget.enemyPos = Vector3::zero();
            EnemyTarget.moveForward = Vector3::zero();
            minDistance = std::numeric_limits<float>::infinity();
            minDirection = std::numeric_limits<float>::infinity();
            minHealth = std::numeric_limits<float>::infinity();
            minHealth2 = std::numeric_limits<float>::infinity();
            minHealthPercent = std::numeric_limits<float>::infinity();
            Entity = nullptr;
        }


}




    return _UpdateLogic(instance, delta);
}





