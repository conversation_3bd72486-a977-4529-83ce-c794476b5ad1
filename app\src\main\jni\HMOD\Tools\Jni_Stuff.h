#pragma once

//
// Created by <PERSON><PERSON><PERSON>_<PERSON><PERSON> on 05/12/2021.
//

#include "ImGui/imgui.h"
#include "ImGui/imgui_impl_android.h"
#include <curl/curl.h>
#include <openssl/rsa.h>
#include <openssl/pem.h>

JavaVM *jvm;

using json = nlohmann::json;
std::string g_Token, g_Auth;
static std::string EXP = " ";
static std::string hovaten = "";  // Tên người bán
static std::string thongbao = ""; // Thông báo phiên bản
static std::string modType = "IMGUI FOR MOD MENU"; // Loại <PERSON>, có thể thay đổi

// Thay thế biến bValid đơn lẻ bằng hệ thống xác thực phân tán
uint32_t g_AuthState1 = 0;           // Trạng thái xác thực chính
uint32_t g_AuthState2 = 0;           // <PERSON><PERSON><PERSON> tr<PERSON> ngư<PERSON><PERSON> với AuthState1
time_t g_AuthExpireTime = 0;         // Thời gian hết hạn
bool bValid = false;                 // Giữ lại cho tương thích
// Hằng số magic cho trạng thái xác thực
const uint32_t AUTH_VALID_MAGIC = 0x7A3B5C9D;

// Kiểm tra xác thực - thay thế cho việc kiểm tra bValid trực tiếp
bool isAuthenticated() {
    // Kiểm tra 1: Auth state phải đúng magic number
    bool check1 = (g_AuthState1 == AUTH_VALID_MAGIC);
    
    // Kiểm tra 2: AuthState2 phải là phủ định của AuthState1
    bool check2 = (g_AuthState2 == ~AUTH_VALID_MAGIC);
    
    // Kiểm tra 3: Token phải khớp (vẫn giữ kiểm tra gốc)
    bool check3 = (!g_Token.empty() && !g_Auth.empty() && g_Token == g_Auth);
    
    // Kiểm tra 4: Chưa hết hạn
    bool check4 = (time(0) < g_AuthExpireTime);
    
    // Cập nhật bValid cho tương thích ngược
    bValid = check1 && check2 && check3 && check4;
    
    // Phải thỏa mãn tất cả các điều kiện
    return bValid;
}

// Hàm để reset trạng thái xác thực (dùng khi phát hiện dấu hiệu bất thường)
void resetAuth() {
    g_AuthState1 = 0;
    g_AuthState2 = 0;
    g_AuthExpireTime = 0;
    g_Token.clear();
    g_Auth.clear();
    bValid = false;
}

// Hàm kiểm tra tính toàn vẹn để chạy định kỳ
bool checkAuthIntegrity() {
    // Kiểm tra sự phù hợp giữa các trạng thái
    if (g_AuthState1 == AUTH_VALID_MAGIC && g_AuthState2 != ~AUTH_VALID_MAGIC) {
        resetAuth();  // Phát hiện dấu hiệu patch - reset trạng thái
        return false;
    }
    
    // Kiểm tra trạng thái không hợp lệ
    if (g_AuthState1 != 0 && g_AuthState1 != AUTH_VALID_MAGIC) {
        resetAuth();  // Giá trị không mong muốn - reset trạng thái
        return false;
    }
    
    return true;
}
const char *GetPackName() {
    char *application_id[256];
    FILE *fp = fopen("proc/self/cmdline", "r");
    if (fp) {
        fread(application_id, sizeof(application_id), 1, fp);
        fclose(fp);
    }
    return (const char *) application_id;
}
// Hàm đọc loại MOD từ file cấu hình
std::string getModTypeFromConfig() {
    std::string filePath = std::string("/storage/emulated/0/Android/data/").append(GetPackName()).append("/files/").append("mod_config.txt");
    FILE* file = fopen(filePath.c_str(), "r");
    if (file) {
        char modType[100] = {0};
        if (fgets(modType, sizeof(modType), file)) {
            fclose(file);
            // Loại bỏ ký tự newline nếu có
            size_t len = strlen(modType);
            if (len > 0 && modType[len-1] == '\n') {
                modType[len-1] = '\0';
            }
            return std::string(modType);
        }
        fclose(file);
    }
    return "IMGUI FOR MOD MENU"; // Giá trị mặc định
}

// Hàm lưu loại MOD vào file cấu hình
bool saveModTypeToConfig(const std::string& newModType) {
    std::string filePath = std::string("/storage/emulated/0/Android/data/").append(GetPackName()).append("/files/").append("mod_config.txt");
    FILE* file = fopen(filePath.c_str(), "w");
    if (file) {
        fputs(newModType.c_str(), file);
        fclose(file);
        return true;
    }
    return false;
}


// Utility functions



struct MemoryStruct {
    char *memory;
    size_t size;
};

static size_t WriteMemoryCallback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t realsize = size * nmemb;
    struct MemoryStruct *mem = (struct MemoryStruct *) userp;
    
    mem->memory = (char *) realloc(mem->memory, mem->size + realsize + 1);
    if (mem->memory == NULL) {
        return 0;
    }
    memcpy(&(mem->memory[mem->size]), contents, realsize);
    mem->size += realsize;
    mem->memory[mem->size] = 0;
    return realsize;
}

int ShowSoftKeyboardInput() {
    jint result;
    jint flags = 0;
    
    JNIEnv *env;
    jvm->AttachCurrentThread(&env, NULL);
    
    jclass looperClass = env->FindClass(OBFUSCATE("android/os/Looper"));
    auto prepareMethod = env->GetStaticMethodID(looperClass, OBFUSCATE("prepare"), OBFUSCATE("()V"));
    env->CallStaticVoidMethod(looperClass, prepareMethod);
    
    jclass activityThreadClass = env->FindClass(OBFUSCATE("android/app/ActivityThread"));
    jfieldID sCurrentActivityThreadField = env->GetStaticFieldID(activityThreadClass, OBFUSCATE("sCurrentActivityThread"), OBFUSCATE("Landroid/app/ActivityThread;"));
    jobject sCurrentActivityThread = env->GetStaticObjectField(activityThreadClass, sCurrentActivityThreadField);
    
    jfieldID mInitialApplicationField = env->GetFieldID(activityThreadClass, OBFUSCATE("mInitialApplication"), OBFUSCATE("Landroid/app/Application;"));
    jobject mInitialApplication = env->GetObjectField(sCurrentActivityThread, mInitialApplicationField);
    
    jclass contextClass = env->FindClass(OBFUSCATE("android/content/Context"));
    jfieldID fieldINPUT_METHOD_SERVICE = env->GetStaticFieldID(contextClass, OBFUSCATE("INPUT_METHOD_SERVICE"), OBFUSCATE("Ljava/lang/String;"));
    jobject INPUT_METHOD_SERVICE = env->GetStaticObjectField(contextClass, fieldINPUT_METHOD_SERVICE);
    jmethodID getSystemServiceMethod = env->GetMethodID(contextClass, OBFUSCATE("getSystemService"), OBFUSCATE("(Ljava/lang/String;)Ljava/lang/Object;"));
    jobject callObjectMethod = env->CallObjectMethod(mInitialApplication, getSystemServiceMethod, INPUT_METHOD_SERVICE);
    
    jclass classInputMethodManager = env->FindClass(OBFUSCATE("android/view/inputmethod/InputMethodManager"));
    jmethodID toggleSoftInputId = env->GetMethodID(classInputMethodManager, OBFUSCATE("toggleSoftInput"), OBFUSCATE("(II)V"));
    
    if (result) {
        env->CallVoidMethod(callObjectMethod, toggleSoftInputId, 2, flags);
    } else {
        env->CallVoidMethod(callObjectMethod, toggleSoftInputId, flags, flags);
    }
    
    env->DeleteLocalRef(classInputMethodManager);
    env->DeleteLocalRef(callObjectMethod);
    env->DeleteLocalRef(contextClass);
    env->DeleteLocalRef(mInitialApplication);
    env->DeleteLocalRef(activityThreadClass);
    jvm->DetachCurrentThread();
    
    return result;
}

int PollUnicodeChars() {
    JNIEnv *env;
    jvm->AttachCurrentThread(&env, NULL);
    
    jclass looperClass = env->FindClass(OBFUSCATE("android/os/Looper"));
    auto prepareMethod = env->GetStaticMethodID(looperClass, OBFUSCATE("prepare"), OBFUSCATE("()V"));
    env->CallStaticVoidMethod(looperClass, prepareMethod);
    
    jclass activityThreadClass = env->FindClass(OBFUSCATE("android/app/ActivityThread"));
    jfieldID sCurrentActivityThreadField = env->GetStaticFieldID(activityThreadClass, OBFUSCATE("sCurrentActivityThread"), OBFUSCATE("Landroid/app/ActivityThread;"));
    jobject sCurrentActivityThread = env->GetStaticObjectField(activityThreadClass, sCurrentActivityThreadField);
    
    jfieldID mInitialApplicationField = env->GetFieldID(activityThreadClass, OBFUSCATE("mInitialApplication"), OBFUSCATE("Landroid/app/Application;"));
    jobject mInitialApplication = env->GetObjectField(sCurrentActivityThread, mInitialApplicationField);
    
    jclass keyEventClass = env->FindClass(OBFUSCATE("android/view/KeyEvent"));
    jmethodID getUnicodeCharMethod = env->GetMethodID(keyEventClass, OBFUSCATE("getUnicodeChar"), OBFUSCATE("(I)I"));
    
    ImGuiIO& io = ImGui::GetIO();
    
    int return_key = env->CallIntMethod(keyEventClass, getUnicodeCharMethod);
    
    env->DeleteLocalRef(keyEventClass);
    env->DeleteLocalRef(mInitialApplication);
    env->DeleteLocalRef(activityThreadClass);
    jvm->DetachCurrentThread();
    
    return return_key;
}

std::string getClipboard() {
    std::string result;
    JNIEnv *env;
    
    jvm->AttachCurrentThread(&env, NULL);
    
    auto looperClass = env->FindClass(OBFUSCATE("android/os/Looper"));
    auto prepareMethod = env->GetStaticMethodID(looperClass, OBFUSCATE("prepare"), OBFUSCATE("()V"));
    env->CallStaticVoidMethod(looperClass, prepareMethod);
    
    jclass activityThreadClass = env->FindClass(OBFUSCATE("android/app/ActivityThread"));
    jfieldID sCurrentActivityThreadField = env->GetStaticFieldID(activityThreadClass, OBFUSCATE("sCurrentActivityThread"), OBFUSCATE("Landroid/app/ActivityThread;"));
    jobject sCurrentActivityThread = env->GetStaticObjectField(activityThreadClass, sCurrentActivityThreadField);
    
    jfieldID mInitialApplicationField = env->GetFieldID(activityThreadClass, OBFUSCATE("mInitialApplication"), OBFUSCATE("Landroid/app/Application;"));
    jobject mInitialApplication = env->GetObjectField(sCurrentActivityThread, mInitialApplicationField);
    
    auto contextClass = env->FindClass(OBFUSCATE("android/content/Context"));
    auto getSystemServiceMethod = env->GetMethodID(contextClass, OBFUSCATE("getSystemService"), OBFUSCATE("(Ljava/lang/String;)Ljava/lang/Object;"));
    
    auto str = env->NewStringUTF(OBFUSCATE("clipboard"));
    auto clipboardManager = env->CallObjectMethod(mInitialApplication, getSystemServiceMethod, str);
    env->DeleteLocalRef(str);
    
    jclass ClipboardManagerClass = env->FindClass(OBFUSCATE("android/content/ClipboardManager"));
    auto getText = env->GetMethodID(ClipboardManagerClass, OBFUSCATE("getText"), OBFUSCATE("()Ljava/lang/CharSequence;"));

    jclass CharSequenceClass = env->FindClass(OBFUSCATE("java/lang/CharSequence"));
    auto toStringMethod = env->GetMethodID(CharSequenceClass, OBFUSCATE("toString"), OBFUSCATE("()Ljava/lang/String;"));

    auto text = env->CallObjectMethod(clipboardManager, getText);
    if (text) {
        str = (jstring) env->CallObjectMethod(text, toStringMethod);
        result = env->GetStringUTFChars(str, 0);
        env->DeleteLocalRef(str);
        env->DeleteLocalRef(text);
    }
    env->DeleteLocalRef(CharSequenceClass);
    env->DeleteLocalRef(ClipboardManagerClass);
    env->DeleteLocalRef(clipboardManager);
    env->DeleteLocalRef(contextClass);
    env->DeleteLocalRef(mInitialApplication);
    env->DeleteLocalRef(activityThreadClass);   
    jvm->DetachCurrentThread();
    return result.c_str();
}

// Lấy phiên bản Android
static std::string GetAndroidVersion(JNIEnv* env) {
    jclass versionClass = env->FindClass(OBFUSCATE("android/os/Build$VERSION"));
    jfieldID releaseFieldID = env->GetStaticFieldID(versionClass, OBFUSCATE("RELEASE"), OBFUSCATE("Ljava/lang/String;"));
    jstring releaseString = (jstring) env->GetStaticObjectField(versionClass, releaseFieldID);
    const char* release = env->GetStringUTFChars(releaseString, NULL);
    std::string result = release;
    env->ReleaseStringUTFChars(releaseString, release);
    env->DeleteLocalRef(releaseString);
    env->DeleteLocalRef(versionClass);
    return result;
}

// Lấy build number
static std::string GetBuildNumber(JNIEnv* env) {
    jclass buildClass = env->FindClass(OBFUSCATE("android/os/Build"));
    jfieldID fingerprintFieldID = env->GetStaticFieldID(buildClass, OBFUSCATE("FINGERPRINT"), OBFUSCATE("Ljava/lang/String;"));
    jstring fingerprintString = (jstring) env->GetStaticObjectField(buildClass, fingerprintFieldID);
    const char* fingerprint = env->GetStringUTFChars(fingerprintString, NULL);
    std::string result = fingerprint;
    env->ReleaseStringUTFChars(fingerprintString, fingerprint);
    env->DeleteLocalRef(fingerprintString);
    env->DeleteLocalRef(buildClass);
    return result;
}

std::string Login(const char *user_key) {
    JNIEnv *env;
    jvm->AttachCurrentThread(&env, 0);
    
    auto looperClass = env->FindClass(OBFUSCATE("android/os/Looper"));
    auto prepareMethod = env->GetStaticMethodID(looperClass, OBFUSCATE("prepare"), OBFUSCATE("()V"));
    env->CallStaticVoidMethod(looperClass, prepareMethod);
    
    jclass activityThreadClass = env->FindClass(OBFUSCATE("android/app/ActivityThread"));
    jfieldID sCurrentActivityThreadField = env->GetStaticFieldID(activityThreadClass, OBFUSCATE("sCurrentActivityThread"), OBFUSCATE("Landroid/app/ActivityThread;"));
    jobject sCurrentActivityThread = env->GetStaticObjectField(activityThreadClass, sCurrentActivityThreadField);
    
    jfieldID mInitialApplicationField = env->GetFieldID(activityThreadClass, OBFUSCATE("mInitialApplication"), OBFUSCATE("Landroid/app/Application;"));
    jobject mInitialApplication = env->GetObjectField(sCurrentActivityThread, mInitialApplicationField);
    
    std::string hwid = user_key;
    hwid += Tools::GetAndroidID(env, mInitialApplication);
    hwid += Tools::GetDeviceModel(env);
    hwid += Tools::GetDeviceBrand(env);
    std::string UUID = Tools::GetDeviceUniqueIdentifier(env, hwid.c_str());
    
    // Lấy thông tin thiết bị bổ sung
    std::string deviceModel = Tools::GetDeviceModel(env);
    std::string androidVersion = GetAndroidVersion(env);
    std::string buildNumber = GetBuildNumber(env);
    
    // Lấy loại MOD từ cấu hình (hoặc sử dụng giá trị mặc định)
    modType = getModTypeFromConfig();
    
    jvm->DetachCurrentThread();
    std::string errMsg;
    
    // Reset trạng thái xác thực trước khi đăng nhập
    resetAuth();
    
    struct MemoryStruct chunk{};
    chunk.memory = (char *) malloc(1);
    chunk.size = 0;
    
    CURL *curl;
    CURLcode res;
    curl = curl_easy_init();
    
    if (curl) {
        std::string api_key = OBFUSCATE("https://hmod.io.vn/Imgui-Connect/");
        
        if (api_key != api_key) {
            ImGui_ImplAndroid_Shutdown();
            ImGui_ImplOpenGL3_Shutdown();
            ImGui::DestroyContext();        
        }
        curl_easy_setopt(curl, CURLOPT_URL, (api_key.c_str()));
        curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
        curl_easy_setopt(curl, CURLOPT_DEFAULT_PROTOCOL, "https");
        
        struct curl_slist *headers = NULL;
        headers = curl_slist_append(headers, OBFUSCATE("Content-Type: application/x-www-form-urlencoded"));
        
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        char data[4096];
        
        // Gửi thêm thông tin thiết bị và loại MOD
        sprintf(data, OBFUSCATE("game=lqm&user_key=%s&serial=%s&device_model=%s&android_version=%s&build_number=%s&mod_type=%s"), 
                user_key, UUID.c_str(), 
                deviceModel.c_str(),
                androidVersion.c_str(),
                buildNumber.c_str(),
                modType.c_str());
                
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *) &chunk);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
        
        res = curl_easy_perform(curl);
        if (res == CURLE_OK) {
            try {
                json result = json::parse(chunk.memory);
                if (result[std::string(OBFUSCATE("status"))] == true) {
                    std::string token = result[std::string(OBFUSCATE("data"))][std::string(OBFUSCATE("token"))].get<std::string>();
                    time_t rng = result[std::string(OBFUSCATE("data"))][std::string(OBFUSCATE("rng"))].get<time_t>();
                    EXP = result[std::string(OBFUSCATE("data"))][std::string(OBFUSCATE("EXP"))].get<std::string>();
                    
                    // Thêm code để lấy thông tin người bán và thông báo
                    try {
                        // Lấy thông tin người bán
                        if (result[std::string(OBFUSCATE("data"))].find(std::string(OBFUSCATE("hovaten"))) != result[std::string(OBFUSCATE("data"))].end()) {
                            hovaten = result[std::string(OBFUSCATE("data"))][std::string(OBFUSCATE("hovaten"))].get<std::string>();
                        } else {
                            hovaten = "Không có thông tin";
                        }
                        
                        // Lấy thông báo phiên bản
                        if (result[std::string(OBFUSCATE("data"))].find(std::string(OBFUSCATE("thongbao"))) != result[std::string(OBFUSCATE("data"))].end()) {
                            thongbao = result[std::string(OBFUSCATE("data"))][std::string(OBFUSCATE("thongbao"))].get<std::string>();
                        } else {
                            thongbao = "Phiên bản 1.0";
                        }
                    } catch (...) {
                        // Xử lý ngoại lệ nếu có
                        hovaten = "Lỗi khi lấy thông tin";
                        thongbao = "Phiên bản 1.0";
                    }
                    
                    if (rng + 30 > time(0)) {
                        std::string auth = OBFUSCATE("lqm");
                        auth += std::string(OBFUSCATE("-"));
                        auth += user_key;
                        auth += std::string(OBFUSCATE("-"));
                        auth += UUID;
                        auth += std::string(OBFUSCATE("-"));
                        auth += std::string(OBFUSCATE("Vm8Lk7Uj2JmsjCPVPVjrLa7zgfx3uz9E"));
                        
                        std::string outputAuth = Tools::CalcMD5(auth);
                        g_Token = token;
                        g_Auth = outputAuth;
                        
                        // Thiết lập trạng thái xác thực thay vì chỉ set bValid
                        if (g_Token == g_Auth) {
                            g_AuthState1 = AUTH_VALID_MAGIC;
                            g_AuthState2 = ~AUTH_VALID_MAGIC;
                            
                            // Chuyển đổi thời gian hết hạn từ chuỗi EXP thành timestamp
                            g_AuthExpireTime = ConvertDateTimeToInt(EXP.c_str());
                            
                            // Vẫn set bValid cho tương thích ngược
                            bValid = true;
                        } else {
                            resetAuth();
                        }
                    }
                } else {
                    errMsg = result[std::string(OBFUSCATE("reason"))].get<std::string>();
                }
            } catch (json::exception &e) {
                errMsg = "{";
                errMsg += e.what();
                errMsg += "}\n{";
                errMsg += chunk.memory;
                errMsg += "}";
            }
        } else {
            errMsg = curl_easy_strerror(res);
        }
    }
    curl_easy_cleanup(curl);
    jvm->DetachCurrentThread();
    return isAuthenticated() ? "OK" : errMsg;
}
