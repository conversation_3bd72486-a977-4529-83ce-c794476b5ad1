#pragma once

#define targetLib OBFUSCATE("libil2cpp.so")
uintptr_t il2cppMap;

// Cấu trúc cấu hình ESP mới
struct ESPConfig {
    struct {
        bool Enable_ESP = true;
        bool MinimapIcon = true;
        bool PlayerLine = true;
        bool PlayerBox = true;
        bool PlayerHealth = true;
        bool PlayerName = false;
        bool PlayerDistance = true;
        bool PlayerAlert = true;
        bool showCd = true;
        bool IgnoreInvisible = false;
        bool Icon = true;


        // Cấu hình kích thước mini map và big map
        float MapSize = 240.0f;
        float BigMapSize = 240.0f;
        float MapOffsetX = 0.0f;
        float MapOffsetY = 0.0f;
        float IconSize = 10.0f;

    } ESPMenu;

    struct {
        float enemy[4] = {1.0f, 0.0f, 0.0f, 1.0f}; // Màu kẻ địch (đỏ) RGBA
        float team[4] = {0.0f, 1.0f, 0.0f, 1.0f};  // <PERSON><PERSON>u đồng đội (xanh lá) RGBA
        float line[4] = {1.0f, 1.0f, 1.0f, 0.8f};  // Màu đường kẻ (trắng) RGBA
        float hp_high[4] = {0.0f, 1.0f, 0.0f, 1.0f}; // Màu máu cao (xanh lá)
        float hp_mid[4] = {1.0f, 1.0f, 0.0f, 1.0f};  // Màu máu trung bình (vàng)
        float hp_low[4] = {1.0f, 0.0f, 0.0f, 1.0f};  // Màu máu thấp (đỏ)
    } Color;
};

// Tạo biến cấu hình toàn cục
ESPConfig Config;

struct actor_data {
    uintptr_t instance;
    bool Visible = false;
    int camp = 2;
    int ViewSight = 0;
    std::string HeroName = "Undefine";
};

std::vector<actor_data*> actor_datav;

bool isActorDataExists(uintptr_t instance) {
    for (const auto& data : actor_datav) {
        if (data->instance == instance) {
            return true;  // Tìm thấy instance trong actor_datav
        }
    }
    return false;  // Không tìm thấy instance trong actor_datav
}

void removeActorData(uintptr_t instance) {
    for (auto it = actor_datav.begin(); it != actor_datav.end(); ++it) {
        if ((*it)->instance == instance) {
            delete *it; // Xóa bộ nhớ đã cấp phát cho con trỏ actor_data
            actor_datav.erase(it);
            break;  // Tìm thấy và xóa actor_data từ actor_datav
        }
    }
}

// Các biến cấu hình cũ (giữ để tương thích ngược)
int CameraHeight;
bool PlayerLine;
bool PlayerBox;
bool PlayerHealth;
bool PlayerName;
bool HeroName;
bool PlayerDistance;
bool PlayerAlert;
bool showCd;
bool IgnoreInvisible;
bool Bypass;
bool MinimapIcon;
bool Enable_ESP;
bool Icon;

// Biến điều khiển hiển thị đường kẻ AIM
extern bool showTargetLine;

// Biến AIM từ Hooker.h
extern bool AimElsu;
extern int aimType;
extern struct EntityInfo EnemyTarget;

